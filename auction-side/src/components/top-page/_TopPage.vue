<script setup lang="ts">
  import ViewOnlyProductList from '@/components/common/product-list/ViewOnlyProductList.vue'
  import useFavorite from '@/composables/favorite'
  import useSearchResultState from '@/composables/state/useSearchResultState'
  import useTopPageItems from '@/composables/top-page/useTopPageItems'
  import {PATH_NAME} from '@/defined/const'
  import router from '@/router'
  import {computed, onMounted, onUnmounted, ref} from 'vue'
  import {RouterLink} from 'vue-router'
  import Footer from './Footer.vue'
  import Header from './Header.vue'
  import {useSlickSlider} from './useSlickSlider'
  import {
    topPageCategories,
    type TopPageCategoryItem,
  } from './utils/category-list'

  const {initSlider, cleanupSlider} = useSlickSlider()
  const {loading, fetchNewItems, fetchRecommendedItems} = useTopPageItems()
  const {toggleFavorite} = useFavorite()
  const searchState = useSearchResultState()

  // Separate state for new and recommended items
  const newItems = ref([])
  const recommendedItems = ref([])
  const newItemsLoading = ref(false)
  const recommendedItemsLoading = ref(false)

  // Computed properties for filtered items
  const newItemsFiltered = computed(() => {
    return newItems.value.filter(item => item.isAdItem === true)
  })

  // Fetch new items and store separately
  const loadNewItems = async () => {
    newItemsLoading.value = true
    try {
      await fetchNewItems()
      newItems.value = [...searchState.productList.all]
    } catch (error) {
      console.error('Error loading new items:', error)
    } finally {
      newItemsLoading.value = false
    }
  }

  // Event handlers
  const handleFavoriteToggle = async (
    exhibitionItemNo: string,
    currentFavorited: boolean
  ) => {
    console.log('handleFavoriteToggle', exhibitionItemNo, currentFavorited)
    await toggleFavorite(exhibitionItemNo, currentFavorited)
    // Refresh both sections
    await loadNewItems()
    // await loadRecommendedItems()
    // Reinitialize slider after data refresh
    // await reinitializeSlider()
  }

  const handleClickItem = item => {
    console.log('Item clicked:', item)
    router.push(PATH_NAME.DETAILS)
  }

  const handleRefresh = async () => {
    console.log('Refresh handler called')
    await loadNewItems()
    // await loadRecommendedItems()
    // await reinitializeSlider()
  }

  const handleSearch = () => {
    console.log('Search initiated')
    router.push(PATH_NAME.AUCTION_LIST)
  }

  const handleCategoryClick = (category: TopPageCategoryItem) => {
    console.log('Category clicked:', category.name)
    router.push(category.routePath)
  }

  const handleMoreCategory = () => {
    console.log('More category clicked')
    router.push(PATH_NAME.CATEGORY_LIST)
  }

  // Handlers for ViewOnlyProductList
  const handlers = {
    onFavoriteToggle: handleFavoriteToggle,
    onRefresh: handleRefresh,
    onItemClick: handleClickItem,
  }

  // // Reinitialize slider when recommended items change
  // const reinitializeSlider = async () => {
  //   await nextTick()
  //   setTimeout(() => {
  //     cleanupSlider('.list-item-gallery')
  //     initSlider('.list-item-gallery')
  //   }, 50)
  // }

  // onMounted(async () => {
  //   // await Promise.all([loadNewItems(), loadRecommendedItems()])
  //   // Wait for Vue to complete DOM updates before initializing slider
  //   // await nextTick()

  //   // setTimeout(() => {
  //   //   initSlider('.list-item-gallery')
  //   // }, 100)
  //   await initSlider('.list-item-gallery')
  // })
  onMounted(async () => {
    await loadNewItems()
    await initSlider('.list-item-gallery')
  })

  onUnmounted(() => {
    cleanupSlider('.list-item-gallery')
  })
</script>
<template>
  <!-- Header -->
  <Header />
  <main id="main">
    <section id="heroes">
      <p class="catch">Auction Coming Soon!</p>
    </section>
    <section class="nav-list-mode-wrap">
      <div class="container">
        <RouterLink :to="PATH_NAME.AUCTION_LIST"
          ><span>競り上がり式</span><span>オークション</span></RouterLink
        >
        <RouterLink :to="PATH_NAME.AUCTION_LIST"
          ><span>封印入札式</span><span>オークション</span></RouterLink
        >
      </div>
    </section>
    <section id="search">
      <div class="cont-wrap">
        <div class="search-keyword">
          <input
            type="text"
            data-id="shop-search-keyword"
            value=""
            class="side-search-keyword"
            placeholder="商品名・キーワードで探す"
          />
          <!-- カテゴリボタン start -->
          <div class="btn-category">
            <a href="javascript:void(0)"><span>すべてのカテゴリ</span></a>
          </div>
          <div class="filter-panel">
            <div class="panel-body">
              <div class="list-wrap">
                <ul class="list">
                  <li class="label pre">すべてのカテゴリ</li>
                  <li class="label">LOUIS VUITTON</li>
                  <li class="label">CHANEL</li>
                </ul>
                <ul class="list">
                  <li class="label">HERMES</li>
                  <li class="label">GUCCI</li>
                  <li class="label">PRADA</li>
                </ul>
                <ul class="list">
                  <li class="label">BURBERRY</li>
                  <li class="label">FENDI</li>
                  <li class="label">CELINE</li>
                </ul>
                <ul class="list">
                  <li class="label">Christian Dior</li>
                  <li class="label">ETRO</li>
                  <li class="label">FENDI</li>
                </ul>
                <ul class="list">
                  <li class="label">GIVENCHY</li>
                  <li class="label">FURLA</li>
                  <li class="label">FERRAGAMO</li>
                </ul>
              </div>
              <p class="close-filter"><span></span></p>
            </div>
          </div>
          <!-- カテゴリボタン end -->
        </div>
        <button @click="handleSearch" class="btn-search">
          <img src="@/assets/img/common/icn_search.svg" />
        </button>
      </div>
    </section>

    <!-- カテゴリーから探す Start -->
    <section class="search-category">
      <p class="ttl">カテゴリーから探す</p>
      <ul class="list-category">
        <li v-for="category in topPageCategories" :key="category.id">
          <a
            @click.prevent="handleCategoryClick(category)"
            :href="category.routePath"
            :title="category.description"
          >
            <figure>
              <img :src="category.imagePath" :alt="category.name" />
            </figure>
            <p>{{ category.name }}</p>
          </a>
        </li>
      </ul>
      <button class="btn more-category" @click="handleMoreCategory">
        <span>もっと見る</span>
      </button>
    </section>
    <!-- カテゴリーから探す End -->
    <!-- Information Start -->
    <section id="info">
      <div class="container-grid">
        <h2>NEWS<span>更新情報</span></h2>
        <ul class="info-item">
          <li>
            <a href="A"
              ><span class="notice-day">2025/12/01</span
              >【お詫び】一部商品の配送遅延について</a
            >
          </li>
          <li>
            <a href="A"
              ><span class="notice-day">2025/12/01</span>【新商品】〇〇シリーズ
              販売開始のご案内</a
            >
          </li>
          <li>
            <a href="A"
              ><span class="notice-day">2025/12/01</span
              >【重要】システムメンテナンスのお知らせ</a
            >
          </li>
        </ul>
        <div class="more-btn-wrap"><a href="A" class="btn">もっと見る</a></div>
      </div>
    </section>
    <!-- Information End -->

    <!-- おすすめ Start -->
    <section id="list-recommend" class="list-item list-slider">
      <h2>
        <p class="ttl">おすすめ商品</p>
      </h2>
      <div class="container">
        <div class="item-list sliderArea">
          <ul class="list-item-gallery top slider">
            <li
              class=""
              v-for="item in newItemsFiltered"
              :key="item.exhibition_item_no"
            >
              <a href="./stock/detail_in.html" class="pct-wrap">
                <figure>
                  <img src="@/assets/img/item/top_item01.png" alt="" />
                  <div class="tab-f"><span class="title-a">New</span></div>
                </figure>
                <div class="panel-disc">
                  <p class="item-name">
                    {{ item.free_field.productName }}
                    <span v-if="item.free_field.shipping_free" class="tab-item"
                      >送料無料</span
                    >
                  </p>
                  <ul class="tab-wrap">
                    <li class="tab-main">未使用</li>
                    <li class="tab-sub">新品</li>
                  </ul>
                  <p class="current-price">
                    <span class="price-c">現在</span
                    ><span class="price-v">380,000</span
                    ><span class="price-u">円</span>
                  </p>
                  <ul class="tab-wrap">
                    <li class="top">あなたがTOP</li>
                    <li class="min-bid">最低落札超え</li>
                  </ul>
                </div>
                <ul class="pre-bid">
                  <li class="end-v">
                    <p><span class="date red">残り2時間</span></p>
                  </li>
                  <li class="bid-v">
                    <p>884</p>
                  </li>
                </ul>
              </a>
              <div class="btn-foreground-wrap">
                <button class="btn refresh"></button>
                <button class="btn favorite"></button>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <!-- おすすめ End -->

    <!-- Item List Start -->
    <section id="list-new" class="list-item">
      <h2>
        <p class="ttl">新着商品</p>
        <RouterLink :to="PATH_NAME.AUCTION_LIST" class="btn more"
          >もっと見る</RouterLink
        >
      </h2>

      <div class="container">
        <div v-if="newItemsLoading" class="loading">読み込み中...</div>
        <div v-else class="item-list">
          <ViewOnlyProductList
            :items="newItemsFiltered"
            view-mode="panel"
            :handlers="handlers"
            custom-classes=""
          />
        </div>
      </div>
    </section>
    <!-- Item List End -->
    <!-- about Start -->
    <section id="about">
      <div class="cont-wrap">
        <h2>
          <p class="ttl">オークションサイトのパッケージが誕生</p>
          <p class="s-ttl">クラウドECオークション</p>
        </h2>
        <div class="read">
          <p class="sb">
            「自社オークションを簡単に開催したい」というご要望に応えるパッケージが誕生しました。
          </p>
          <p class="st">
            オークションに必要な基本機能をパッケージ化し、低コストでで導入いただけるサービスです。サイト構築に手間がかからず、比較的短期間での導入が可能です。
          </p>
          <p class="sb">
            BtoBオークションサイトを実現するカスタマイズにも対応いたします。
          </p>
          <p class="st">
            会員の登録審査機能、お取引先様の管理機能などをご用意しておりますので、お取引先や限定された会員向けのオークションが開催できます。
          </p>
        </div>
        <div class="btn-wrap">
          <a href="./other/about/" class="btn more">もっと見る</a>
        </div>
      </div>
    </section>
    <!-- about End -->
    <!-- signup Start -->
    <section id="signup">
      <div class="cont-wrap">
        <h2>
          <p class="s-ttl">登録はいつでも無料</p>
          <p class="ttl">会員になって、お得に欲しい商品をゲット！</p>
        </h2>
        <div class="read">
          <p class="sb">無料配送特典</p>
          <p class="st">
            会員は、対象商品の配送において、当日配送を除くお届け日時指定便を無料でご利用いただけます。詳細は、<a
              href="/"
              >お届け日時指定便について</a
            >をご覧ください。
          </p>
          <p class="sb">クーポン配布</p>
          <p class="st">
            定期的に割引クーポンを配布しています。クーポンを獲得するには<a
              href="./signin"
              >ログイン</a
            >が必要です。
          </p>
        </div>
        <div class="btn-wrap">
          <a href="./other/member/" class="btn more">会員についてもっと見る</a>
          <a href="./other/member/" class="btn signup">新規会員登録</a>
        </div>
      </div>
    </section>
    <!-- signup End -->
  </main>

  <Footer />
</template>
