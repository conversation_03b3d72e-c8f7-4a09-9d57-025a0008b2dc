<script setup lang="ts">
  import type {FormattedAuctionItem} from '@/composables/state/useSearchResultState'
  import {computed} from 'vue'
  import type {ProductListHandlers} from './types'

  /**
   * Props interface for ProductList component
   * Displays auction items that can be bid on - always in row-bid layout
   */
  interface Props {
    /** Array of product items to display */
    items: FormattedAuctionItem[]
    /** Event handlers for user interactions */
    handlers?: ProductListHandlers
    /** Custom CSS classes */
    customClasses?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    customClasses: '',
  })

  // Computed properties for display
  const displayItems = computed(() => {
    const filtered = props.items.filter(item => item.isAdItem === false)
    return filtered
  })

  const getItemLink = (item: FormattedAuctionItem) => {
    return item.link || '#'
  }

  const onItemClickHandler = (item: FormattedAuctionItem) => {
    if (props.handlers?.onItemClick) {
      props.handlers.onItemClick(item)
    }
  }

  const handleFavoriteToggle = async (
    exhibitionItemNo: string,
    currentFavorited: boolean
  ) => {
    if (props.handlers?.onFavoriteToggle) {
      try {
        await props.handlers.onFavoriteToggle(
          exhibitionItemNo,
          currentFavorited
        )
      } catch (error) {
        console.error('Error toggling favorite:', error)
      }
    }
  }

  const handleBid = async (
    item: FormattedAuctionItem,
    bidPrice: string,
    bidQuantity: string
  ) => {
    if (props.handlers?.onBid) {
      try {
        await props.handlers.onBid(item, bidPrice, bidQuantity)
      } catch (error) {
        console.error('Error placing bid:', error)
      }
    }
  }

  const handleRefresh = async () => {
    if (props.handlers?.onRefresh) {
      try {
        await props.handlers.onRefresh()
      } catch (error) {
        console.error('Error refreshing:', error)
      }
    }
  }

  // Format price display
  const formatPrice = (price: number | string) => {
    if (typeof price === 'number') {
      return price.toLocaleString()
    }
    return price || '0'
  }

  // Get container classes - always row-bid for biddable items
  const containerClasses = computed(() => {
    const classes = ['item-list', 'row-bid']
    if (props.customClasses) {
      classes.push(props.customClasses)
    }
    return classes.join(' ')
  })

  // Get item classes
  const getItemClasses = (item: FormattedAuctionItem) => {
    const classes: string[] = ['box', 'auction-item']

    // Check for sold out status
    if (item.status === 'soldout') {
      classes.push('soldout')
    }

    return classes.join(' ')
  }
</script>

<template>
  <!-- Biddable Products Container - Always row-bid layout -->
  <div v-if="displayItems.length > 0" :class="containerClasses">
    <ul>
      <li
        v-for="item in displayItems"
        :key="item.exhibition_item_no"
        :class="getItemClasses(item)"
      >
        <figure>
          <img src="@/assets/img/item/top_item01.png" alt="" />
          <div class="tab-f">
            <span class="title-a">New</span>
          </div>
        </figure>

        <div class="item-p-desc">
          <p class="item-name">
            <a
              :href="getItemLink(item)"
              @click.prevent="onItemClickHandler(item)"
            >
              {{ item.free_field.productName }}
              <span v-if="item.free_field.shipping_free" class="tab-item"
                >送料無料</span
              >
            </a>
          </p>

          <div class="desc-p-top">
            <div class="price-box">
              <p class="price">
                <span class="price-c">現在 : </span>
                <span class="price-v">{{
                  formatPrice(item.bid_status.current_price)
                }}</span>
                <span class="price-u">円</span>
              </p>
              <p v-if="item.free_field.instant_price" class="price">
                <span class="price-c">即決価格 : </span>
                <span class="price-v bl">{{
                  formatPrice(item.free_field.instant_price)
                }}</span>
                <span class="price-u bl">円</span>
              </p>
              <p class="price">
                <span class="price-c">最低入札価格 : </span>
                <span class="price-v bl sm">{{
                  formatPrice(item.free_field.minimum_bid_price || 0)
                }}</span>
                <span class="price-u bl sm">円</span>
              </p>
            </div>
            <ul class="tab-wrap-status">
              <li v-if="item.bid_status.is_top_member" class="top">
                あなたがTOP
              </li>
              <li v-if="item.bid_status.minimum_bid_exceeded" class="min-bid">
                最低落札超え
              </li>
            </ul>
          </div>

          <ul class="tab-wrap">
            <li class="tab-main">未使用に近い</li>
            <li class="tab-sub">ICON</li>
            <li class="tab-wari">ICON</li>
          </ul>

          <ul class="pre-bid">
            <li class="view">
              <p>{{ item.attention_info.view_count || 0 }}</p>
            </li>
            <li class="favo">
              <p>{{ item.attention_info.favorited_count || 0 }}</p>
            </li>
            <li class="bid-v">
              <p>{{ item.attention_info.bid_count }}</p>
            </li>
            <li class="end-v">
              <p>
                <span class="date red">{{ item.endDatePart }}</span>
                <span class="end">（{{ item.endTimePart }} 終了予定）</span>
              </p>
            </li>
          </ul>
          <button
            :class="[
              'btn',
              'favorite',
              'row-bid',
              {active: item.attention_info.is_favorited},
            ]"
            @click="
              handleFavoriteToggle(
                item.exhibition_item_no,
                !!item.attention_info.is_favorited
              )
            "
          ></button>
        </div>

        <!-- Full Bidding Interface for auction items -->
        <div class="place-bid">
          <div class="price">
            <span class="ttl">入札価格</span>
            <input
              type="text"
              :value="item.bidPrice"
              class="price-bid"
              placeholder="1,000"
            />円
          </div>
          <ul class="bidding-unit">
            <li>
              <button class="bid-unit">
                <span class="icn_add"></span>¥10,000
              </button>
            </li>
            <li>
              <button class="bid-unit">
                <span class="icn_add"></span>¥50,000
              </button>
            </li>
            <li>
              <button class="bid-unit">
                <span class="icn_add"></span>¥100,000
              </button>
            </li>
          </ul>
          <div class="button-bid">
            <button
              class="btn"
              @click="handleBid(item, item.bidPrice, item.bidQuantity)"
            >
              <img class="pct" src="@/assets/img/common/icn_bid_w.svg" />
              <span class="bid">入札する</span>
            </button>
            <p class="update" @click="handleRefresh">
              <span>更新</span>
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped>
  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
    color: #333;
  }

  .no-items {
    text-align: center;
    padding: 40px 20px;
    color: #666;
  }
</style>
