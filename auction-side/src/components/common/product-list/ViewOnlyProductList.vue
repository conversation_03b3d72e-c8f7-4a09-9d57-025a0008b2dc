<script setup lang="ts">
  import type {FormattedAuctionItem} from '@/composables/state/useSearchResultState'
  import {computed} from 'vue'
  import type {ProductListHandlers, ViewMode} from './types'

  interface Props {
    items: FormattedAuctionItem[]
    /** Current view mode (panel/row) */
    viewMode: ViewMode
    /** Event handlers for user interactions */
    handlers?: ProductListHandlers
    customClasses?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    customClasses: '',
  })

  // Computed properties for display
  const displayItems = computed(() => {
    const filtered = props.items.filter(item => item.isAdItem === true)
    return filtered
  })

  const onItemClickHandler = (item: FormattedAuctionItem) => {
    if (props.handlers?.onItemClick) {
      props.handlers.onItemClick(item)
    }
  }

  const handleFavoriteToggle = async (
    exhibitionItemNo: string,
    currentFavorited: boolean
  ) => {
    if (props.handlers?.onFavoriteToggle) {
      try {
        await props.handlers.onFavoriteToggle(
          exhibitionItemNo,
          currentFavorited
        )
      } catch (error) {
        console.error('Error toggling favorite:', error)
      }
    }
  }

  const handleRefresh = async () => {
    if (props.handlers?.onRefresh) {
      try {
        await props.handlers.onRefresh()
      } catch (error) {
        console.error('Error refreshing:', error)
      }
    }
  }

  // Format price display
  const formatPrice = (price: number | string) => {
    if (typeof price === 'number') {
      return price.toLocaleString()
    }
    return price || '0'
  }

  // Get item classes
  const getItemClasses = (item: FormattedAuctionItem) => {
    const classes: string[] = []

    if (props.viewMode === 'panel') {
      classes.push('panel-item')
    } else {
      classes.push('box')
    }

    // Check for sold out status
    if (item.status === 'soldout') {
      classes.push('soldout')
    }

    return classes.join(' ')
  }

  // Check if item should have "New" badge
  const hasNewBadge = (item: FormattedAuctionItem) => {
    return true
  }

  // Get dynamic condition tags for an item
  const getConditionTags = (item: FormattedAuctionItem) => {
    const tags: Array<{key: string; label: string; class: string}> = []

    if (item.free_field.condition) {
      tags.push({
        key: 'condition',
        label: item.free_field.condition,
        class: 'tab-main',
      })
    } else {
      // Default condition tags
      tags.push({
        key: 'condition-default',
        label: '未使用に近い',
        class: 'tab-main',
      })
    }

    if (
      item.free_field.productName?.includes('ブランド') ||
      item.free_field.productName?.includes('CHANEL') ||
      item.free_field.productName?.includes('LOUIS VUITTON')
    ) {
      tags.push({
        key: 'brand',
        label: 'ブランド品',
        class: 'tab-sub',
      })
    }

    tags.push({
      key: 'accessories',
      label: '付属品あり',
      class: 'tab-standard',
    })

    return tags
  }

  // Get dynamic status indicators for an item
  const getStatusIndicators = (item: FormattedAuctionItem) => {
    const indicators: Array<{key: string; label: string; class: string}> = []

    if (item.bid_status?.is_top_member) {
      indicators.push({
        key: 'top-member',
        label: 'あなたがTOP',
        class: 'top',
      })
    }

    if (item.bid_status?.minimum_bid_exceeded) {
      indicators.push({
        key: 'min-bid-exceeded',
        label: '最低落札超え',
        class: 'min-bid',
      })
    }

    return indicators
  }

  // Get time remaining for an item
  const getTimeRemaining = (item: FormattedAuctionItem) => {
    if (item.endDatePart) {
      return item.endDatePart
    }

    // Calculate time remaining from end_datetime
    const endTime = new Date(item.end_datetime)
    const now = new Date()
    const diff = endTime.getTime() - now.getTime()

    if (diff <= 0) {
      return '終了'
    }

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `残り${days}日`
    } else if (hours > 0) {
      return `残り${hours}時間`
    } else {
      return `残り${minutes}分`
    }
  }

  // Get formatted end date time
  const getEndDateTime = (item: FormattedAuctionItem) => {
    if (item.endTimePart) {
      return item.endTimePart
    }

    const endTime = new Date(item.end_datetime)
    const month = endTime.getMonth() + 1
    const date = endTime.getDate()
    const hours = endTime.getHours()
    const minutes = endTime.getMinutes()

    return `${month}月${date}日 ${hours}時${minutes.toString().padStart(2, '0')}分`
  }
</script>

<template>
  <!-- View-Only Products, view mode change by viewMode switch -->
  <div v-if="displayItems.length > 0" :class="['item-list', viewMode]">
    <ul>
      <li
        v-for="item in displayItems"
        :key="item.exhibition_item_no"
        :class="getItemClasses(item)"
      >
        <a class="pct-wrap" @click.prevent="onItemClickHandler(item)">
          <figure>
            <img src="@/assets/img/item/top_item01.png" alt="" />
            <div v-if="hasNewBadge(item)" class="tab-f">
              <span class="title-a">New</span>
            </div>
          </figure>
          <div class="panel-disc">
            <p class="item-name">
              {{ item.free_field.productName }}
              <span v-if="item.free_field.shipping_free" class="tab-item">
                送料無料
              </span>
            </p>
            <ul v-if="getConditionTags(item).length > 0" class="tab-wrap">
              <li
                v-for="tag in getConditionTags(item)"
                :key="tag.key"
                :class="tag.class"
              >
                {{ tag.label }}
              </li>
            </ul>
            <p class="current-price">
              <span class="price-c">現在</span>
              <span class="price-v">{{
                formatPrice(item.bid_status.current_price)
              }}</span>
              <span class="price-u">円</span>
            </p>
            <!-- Dynamic status indicators -->
            <ul v-if="getStatusIndicators(item).length > 0" class="tab-wrap">
              <li
                v-for="status in getStatusIndicators(item)"
                :key="status.key"
                :class="status.class"
              >
                {{ status.label }}
              </li>
            </ul>
          </div>
          <ul class="pre-bid">
            <li class="end-v">
              <p>
                <span class="date red">{{ getTimeRemaining(item) }}</span>
              </p>
            </li>
            <li class="bid-v">
              <p>{{ item.attention_info.bid_count }}</p>
            </li>
          </ul>
        </a>
        <div class="btn-foreground-wrap">
          <button class="btn refresh" @click="handleRefresh"></button>
          <button
            class="btn favorite"
            :class="{active: item.attention_info.is_favorited}"
            @click="
              handleFavoriteToggle(
                item.exhibition_item_no,
                !!item.attention_info.is_favorited
              )
            "
          ></button>
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped>
  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
    color: #333;
  }

  /* Ad item indicator styling */
  .ad-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ff6b35;
    border-radius: 4px;
    padding: 2px 6px;
  }

  .title-ad {
    color: white;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
  }

  .no-items {
    text-align: center;
    padding: 40px 20px;
    color: #666;
  }
</style>
