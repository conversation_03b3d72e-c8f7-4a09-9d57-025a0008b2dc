#!/usr/bin/env node

/**
 * Test script to verify API Gateway response format
 * Run this to test the Base class response format fix
 */

const Base = require('./infrastructure/common/auction-side/gateway-resource/common-layer/nodejs/base.js')

function testApiGatewayResponse() {
  console.log('🧪 Testing API Gateway response format...')

  const base = new Base(null, 'en') // No pool needed for response testing

  // Test 1: Success response format
  console.log('✅ Step 1: Testing success response format...')

  const mockCallback = (error, response) => {
    if (error) {
      console.error('❌ Callback error:', error)
      return
    }

    console.log('📋 Response structure:', JSON.stringify(response, null, 2))

    // Validate API Gateway response format
    const requiredFields = ['statusCode', 'headers', 'body', 'isBase64Encoded']
    const missingFields = requiredFields.filter(field => !(field in response))

    if (missingFields.length > 0) {
      console.error('❌ Missing required fields:', missingFields)
      return
    }

    if (response.statusCode !== 200) {
      console.error('❌ Expected statusCode 200, got:', response.statusCode)
      return
    }

    if (!response.headers['Content-Type']) {
      console.error('❌ Missing Content-Type header')
      return
    }

    if (!response.headers['Access-Control-Allow-Origin']) {
      console.error('❌ Missing CORS header')
      return
    }

    console.log('✅ Success response format is correct!')

    // Test 2: Error response format
    console.log('✅ Step 2: Testing error response format...')

    const mockError = {status: 400, message: 'Test error'}
    base.createErrorResponse((err, errorResponse) => {
      if (err) {
        console.error('❌ Error callback error:', err)
        return
      }

      console.log(
        '📋 Error response structure:',
        JSON.stringify(errorResponse, null, 2)
      )

      if (errorResponse.statusCode !== 400) {
        console.error(
          '❌ Expected error statusCode 400, got:',
          errorResponse.statusCode
        )
        return
      }

      console.log('✅ Error response format is correct!')
      console.log('🎉 All API Gateway response format tests passed!')
    }, mockError)
  }

  // Test with sample data
  const testData = {message: 'Test successful', data: [{id: 1, name: 'test'}]}
  base.createSuccessResponse(mockCallback, testData, false) // Don't compress for testing
}

// Run the test
testApiGatewayResponse()
