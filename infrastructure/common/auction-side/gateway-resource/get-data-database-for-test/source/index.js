/* eslint-disable camelcase */
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

exports.handle = (e, ctx, cb) => {
  // Handle both API Gateway events and direct invocation
  const params = e.body || e || {}
  console.log('🔍 Database Diagnostic API - event:', JSON.stringify(e, null, 2))
  console.log('🔍 Database Diagnostic API - params:', params)
  const header = e.headers || {}
  const base = new Base(pool, params.languageCode || 'ja')

  let tenant = null
  let diagnosticResults = {}

  Promise.resolve()
    .then(() => base.startRequest(e))
    .then(() => {
      return base.checkOrigin(header.origin || header.Origin).then(data => {
        tenant = data
        console.log('🏢 Tenant info:', tenant)
        return Promise.resolve()
      })
    })
    .then(() => {
      // Test 1: Check current session settings
      console.log('🧪 Test 1: Checking current session settings...')
      return pool.query(`
        SELECT
          current_setting('app.current_tenant', TRUE) as current_tenant,
          current_setting('app.bypass_rls', TRUE) as bypass_rls,
          current_user as db_user,
          current_database() as db_name,
          now() as current_time
      `)
    })
    .then(sessionData => {
      diagnosticResults.sessionInfo = sessionData[0] || {}
      console.log('📊 Session info:', diagnosticResults.sessionInfo)

      // Test 2: Check RLS policies on key tables
      console.log('🧪 Test 2: Checking RLS policies...')
      return pool.query(`
        SELECT
          schemaname,
          tablename,
          rowsecurity as rls_enabled,
          (SELECT count(*) FROM pg_policies WHERE schemaname = t.schemaname AND tablename = t.tablename) as policy_count
        FROM pg_tables t
        WHERE schemaname = 'public'
        AND tablename IN ('t_exhibition', 't_exhibition_item', 't_item', 't_item_localized')
        ORDER BY tablename
      `)
    })
    .then(rlsData => {
      diagnosticResults.rlsPolicies = rlsData
      console.log('🔒 RLS policies:', diagnosticResults.rlsPolicies)

      // Test 3: Count rows using legacy query (bypasses RLS)
      console.log(
        '🧪 Test 3: Counting rows with legacy query (bypasses RLS)...'
      )
      return pool.query(
        `
        SELECT
          't_exhibition' as table_name,
          count(*) as total_rows,
          count(*) FILTER (WHERE tenant_no = $1) as tenant_rows,
          count(*) FILTER (WHERE delete_flag IS NULL OR delete_flag = 0) as active_rows
        FROM t_exhibition
        UNION ALL
        SELECT
          't_exhibition_item' as table_name,
          count(*) as total_rows,
          count(*) FILTER (WHERE tenant_no = $1) as tenant_rows,
          count(*) FILTER (WHERE delete_flag IS NULL OR delete_flag = 0) as active_rows
        FROM t_exhibition_item
        UNION ALL
        SELECT
          't_item' as table_name,
          count(*) as total_rows,
          count(*) FILTER (WHERE tenant_no = $1) as tenant_rows,
          count(*) FILTER (WHERE delete_flag IS NULL OR delete_flag = 0) as active_rows
        FROM t_item
        UNION ALL
        SELECT
          't_item_localized' as table_name,
          count(*) as total_rows,
          count(*) FILTER (WHERE tenant_no = $1) as tenant_rows,
          count(*) FILTER (WHERE delete_flag IS NULL OR delete_flag = 0) as active_rows
        FROM t_item_localized
        ORDER BY table_name
      `,
        [tenant.tenant_no]
      )
    })
    .then(legacyCountData => {
      diagnosticResults.legacyQueryCounts = legacyCountData
      console.log(
        '📈 Legacy query counts:',
        diagnosticResults.legacyQueryCounts
      )

      // Test 4: Try RLS-aware query
      console.log('🧪 Test 4: Testing RLS-aware query...')
      return pool.rlsQuery(
        tenant.tenant_no,
        `
        SELECT
          'WITH RLS' as query_type,
          't_exhibition' as table_name,
          count(*) as accessible_rows
        FROM t_exhibition
        WHERE delete_flag IS NULL OR delete_flag = 0
        UNION ALL
        SELECT
          'WITH RLS' as query_type,
          't_exhibition_item' as table_name,
          count(*) as accessible_rows
        FROM t_exhibition_item
        WHERE delete_flag IS NULL OR delete_flag = 0
        UNION ALL
        SELECT
          'WITH RLS' as query_type,
          't_item' as table_name,
          count(*) as accessible_rows
        FROM t_item
        WHERE delete_flag IS NULL OR delete_flag = 0
        UNION ALL
        SELECT
          'WITH RLS' as query_type,
          't_item_localized' as table_name,
          count(*) as accessible_rows
        FROM t_item_localized
        WHERE delete_flag IS NULL OR delete_flag = 0
        ORDER BY table_name
      `
      )
    })
    .then(rlsCountData => {
      diagnosticResults.rlsQueryCounts = rlsCountData
      console.log('🔐 RLS query counts:', diagnosticResults.rlsQueryCounts)

      // Test 5: Test the actual f_search_auction_items function with both methods
      console.log('🧪 Test 5: Testing f_search_auction_items function...')
      const testParams = [
        tenant.tenant_no, // in_tenant_no
        null, // in_exhibition_nos
        null, // in_auction_classification
        null, // in_search_keys
        null, // in_areas
        null, // in_category
        null, // in_start_year
        null, // in_end_year
        null, // in_start_price
        null, // in_end_price
        null, // in_favorite
        null, // in_bidding
        null, // in_un_sold_out
        null, // in_recommending
        null, // in_exceeding_lowest_price
        null, // in_search_results_sort
        null, // in_category_sort
        null, // in_end_datetime_not_extend_sort
        null, // in_ended_datetime_sort
        null, // in_nickname_sort
        20, // in_limit
        null, // in_showed_item_nos
        null, // in_exhibition_item_nos
        base.language, // in_lang_code
        10, // in_member_no (hardcoded like in original)
        null, // in_model_list
        null, // in_categories
        null, // in_detail_item_no
        null, // in_brand
        null, // in_brands
      ]

      // First try with legacy query (bypasses RLS)
      return pool.query(
        'SELECT * FROM "f_search_auction_items"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25,$26,$27,$28,$29,$30);',
        testParams
      )
    })
    .then(legacyFunctionResult => {
      const legacyData =
        legacyFunctionResult.length > 0 && legacyFunctionResult[0].data
          ? legacyFunctionResult[0].data
          : {}
      diagnosticResults.legacyFunctionResult = {
        itemsCount: legacyData.items ? legacyData.items.length : 0,
        count: legacyData.count || 0,
        count_init: legacyData.count_init || 0,
        category_group_count: legacyData.category_group
          ? legacyData.category_group.length
          : 0,
        exhibition_group_count: legacyData.exhibition_group
          ? legacyData.exhibition_group.length
          : 0,
        sampleItems: legacyData.items ? legacyData.items.slice(0, 2) : [],
      }
      console.log(
        '🔧 Legacy function result:',
        diagnosticResults.legacyFunctionResult
      )

      // Now try with RLS-aware query
      const testParams = [
        tenant.tenant_no, // in_tenant_no
        null, // in_exhibition_nos
        null, // in_auction_classification
        null, // in_search_keys
        null, // in_areas
        null, // in_category
        null, // in_start_year
        null, // in_end_year
        null, // in_start_price
        null, // in_end_price
        null, // in_favorite
        null, // in_bidding
        null, // in_un_sold_out
        null, // in_recommending
        null, // in_exceeding_lowest_price
        null, // in_search_results_sort
        null, // in_category_sort
        null, // in_end_datetime_not_extend_sort
        null, // in_ended_datetime_sort
        null, // in_nickname_sort
        20, // in_limit
        null, // in_showed_item_nos
        null, // in_exhibition_item_nos
        base.language, // in_lang_code
        10, // in_member_no (hardcoded like in original)
        null, // in_model_list
        null, // in_categories
        null, // in_detail_item_no
        null, // in_brand
        null, // in_brands
      ]

      return pool.rlsQuery(
        tenant.tenant_no,
        'SELECT * FROM "f_search_auction_items"($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25,$26,$27,$28,$29,$30);',
        testParams
      )
    })
    .then(rlsFunctionResult => {
      const rlsData =
        rlsFunctionResult.length > 0 && rlsFunctionResult[0].data
          ? rlsFunctionResult[0].data
          : {}
      diagnosticResults.rlsFunctionResult = {
        itemsCount: rlsData.items ? rlsData.items.length : 0,
        count: rlsData.count || 0,
        count_init: rlsData.count_init || 0,
        category_group_count: rlsData.category_group
          ? rlsData.category_group.length
          : 0,
        exhibition_group_count: rlsData.exhibition_group
          ? rlsData.exhibition_group.length
          : 0,
        sampleItems: rlsData.items ? rlsData.items.slice(0, 2) : [],
      }
      console.log(
        '🔐 RLS function result:',
        diagnosticResults.rlsFunctionResult
      )

      // Test 6: Check specific exhibition data
      console.log('🧪 Test 6: Checking exhibition data...')
      return pool.rlsQuery(
        tenant.tenant_no,
        `
        SELECT
          exhibition_no,
          status,
          start_datetime,
          end_datetime,
          preview_start_datetime,
          preview_end_datetime,
          delete_flag,
          exhibition_classification_info
        FROM t_exhibition
        WHERE (delete_flag IS NULL OR delete_flag = 0)
        ORDER BY exhibition_no
        LIMIT 5
      `
      )
    })
    .then(exhibitionData => {
      diagnosticResults.sampleExhibitions = exhibitionData
      console.log('🏛️ Sample exhibitions:', diagnosticResults.sampleExhibitions)

      // Final response
      const response = {
        success: true,
        tenant_info: {
          tenant_no: tenant.tenant_no,
          tenant_name: tenant.tenant_name || 'Unknown',
        },
        diagnostic_results: diagnosticResults,
        summary: {
          rls_enabled: diagnosticResults.rlsPolicies.every(p => p.rls_enabled),
          legacy_vs_rls_comparison: {
            legacy_items: diagnosticResults.legacyFunctionResult.itemsCount,
            rls_items: diagnosticResults.rlsFunctionResult.itemsCount,
            items_match:
              diagnosticResults.legacyFunctionResult.itemsCount ===
              diagnosticResults.rlsFunctionResult.itemsCount,
          },
          potential_issues: [],
        },
      }

      // Add potential issues to summary
      if (
        diagnosticResults.sessionInfo.current_tenant !==
        String(tenant.tenant_no)
      ) {
        response.summary.potential_issues.push(
          'Tenant context not properly set in session'
        )
      }

      if (
        diagnosticResults.legacyFunctionResult.itemsCount === 0 &&
        diagnosticResults.legacyFunctionResult.count > 0
      ) {
        response.summary.potential_issues.push(
          'Count shows items exist but items array is empty - likely RLS filtering issue'
        )
      }

      if (
        diagnosticResults.rlsFunctionResult.itemsCount >
        diagnosticResults.legacyFunctionResult.itemsCount
      ) {
        response.summary.potential_issues.push(
          'RLS query returns more items than legacy query - unexpected behavior'
        )
      }

      return base.createSuccessResponse(cb, response)
    })
    .catch(error => {
      console.error('❌ Database diagnostic error:', error)
      const errorResponse = {
        success: false,
        error: error.message || 'Unknown error',
        tenant_info: tenant
          ? {
              tenant_no: tenant.tenant_no,
              tenant_name: tenant.tenant_name || 'Unknown',
            }
          : null,
        partial_results: diagnosticResults,
      }
      return base.createSuccessResponse(cb, errorResponse)
    })
}
