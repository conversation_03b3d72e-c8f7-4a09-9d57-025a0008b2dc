/* eslint-disable camelcase */
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)
const writePool = new PgPool(process.env.PGHOST)

exports.handle = (e, ctx, cb) => {
  console.log('📚 log of event : ', e)
  const params = e.body
  const header = e.headers
  const authorizer = {member_no: '10'}
  const base = new Base(pool, params.languageCode)
  const exhibitionNos = []

  const getAuctionClassification = classification => {
    if (classification === null) {
      return null
    }
    if (classification === 'ascending') {
      return 1
    }
    return 2
  }

  console.log(
    'log:  auctionClassification: ',
    getAuctionClassification(params.auction_classification)
  )
  const tenant = {tenant_no: 1}
  const test = {tenant_no: 0}
  test.tenant_no = 1
  console.log('🔔 log of test : ', test)

  Promise.resolve()
    .then(() => base.startRequest(e))
    // .then(() => {
    //   return base.checkOrigin(header.origin || header.Origin).then(data => {
    //     tenant = data
    //     return Promise.resolve()
    //   })
    // })
    .then(() => {
      // Save search history
      console.log('📝 log of tenant info : ', tenant)

      if (params.searchKey) {
        return writePool.rlsQuery(
          tenant.tenant_no,
          'select * from f_insert_member_search_history($1,$2,$3);',
          [tenant.tenant_no, params.searchKey, authorizer.member_no]
        )
      }
      return Promise.resolve()
    })
    .then(() => {
      if (params.exhibitionNos && params.exhibitionNos.length > 0) {
        params.exhibitionNos.map(x => exhibitionNos.push(x))
        console.log('📋 Using provided exhibition numbers:', exhibitionNos)
        return Promise.resolve()
      }
      console.log('🔍 Fetching exhibition numbers from database...')
      return pool
        .rlsQuery(
          tenant.tenant_no,
          `SELECT
            exhibition_no, preview_end_datetime
            FROM t_exhibition
            WHERE delete_flag= 0 AND (status = 0 OR preview_end_datetime >= now ());`
        )
        .then(res => {
          console.log('📊 Raw exhibition query result:', res)
          console.table(res)
          res.map(({exhibition_no}) => exhibitionNos.push(exhibition_no))
          console.log('📋 Final exhibition numbers:', exhibitionNos)
          return Promise.resolve()
        })
    })
    .then(() => {
      let in_search_results_sort = null

      // 商品を並び替える
      if (params.sorter) {
        switch (params.sorter.column) {
          case 'sorted_by_recommended_item':
            in_search_results_sort = 'recommend_sort'
            break
          case 'sorted_by_create_datetime':
            in_search_results_sort = params.sorter.asc
              ? 'create_datetime_asc'
              : 'create_datetime_desc'
            break
          case 'sorted_by_remaining_time':
            in_search_results_sort = params.sorter.asc
              ? 'remain_time_asc'
              : 'remain_time_desc'
            break
          case 'sorted_by_current_price':
            in_search_results_sort = params.sorter.asc
              ? 'current_price_asc'
              : 'current_price_desc'
            break
          case 'sorted_by_bid_count':
            in_search_results_sort = params.sorter.asc
              ? 'bid_count_asc'
              : 'bid_count_desc'
            break
          default:
            console.warn(`Unexpected sorter column: ${params.sorter.column}`)
        }
      }
      console.log('log of in_search_results_sort:', in_search_results_sort)
      const sql_params = [
        tenant.tenant_no,
        exhibitionNos,
        getAuctionClassification(params.auction_classification),
        params.searchKey ? [].concat(params.searchKey) : null,
        params.areas,
        null, // params.category
        params.startYear,
        params.endYear,
        params.startPrice,
        params.endPrice,
        params.favorite,
        params.bidding,
        params.unSoldOut,
        params.recommending,
        params.exceedingLowestPrice,
        in_search_results_sort,
        params.categorySort,
        params.endDatetimeNotExtendSort,
        params.endedDatetimeSort,
        params.nicknameSort,
        params.limit,
        params.showedItemNos,
        params.exhibitionItemNos,
        base.language,
        authorizer.member_no,
        params.modelList,
        params.categoryList,
        null,
        null,
        params.brandList,
      ]
      console.log('sql_params before query f_search_auction_items:', sql_params)

      // DEBUGGING: Add detailed parameter analysis
      console.log('🔍 DEBUG - SQL Parameters Analysis:')
      sql_params.forEach((param, index) => {
        console.log(
          `  [${index}] ${typeof param} ${Array.isArray(param) ? `(array[${param.length}])` : ''}: ${JSON.stringify(param)}`
        )
      })

      // Compare with known working parameters from database test
      console.log('🧪 Comparing with known working parameters:')
      console.log('  - Expected tenant_no: 1, Actual:', sql_params[0])
      console.log(
        '  - Expected exhibition_nos: [67, 100, 133], Actual:',
        sql_params[1]
      )
      console.log('  - Expected member_no: 10, Actual:', sql_params[24])
      console.log('  - Expected lang_code: "ja", Actual:', sql_params[23])
      console.log('  - Expected limit: 20, Actual:', sql_params[20])

      // Check if parameters match the working test case
      const paramsMatch = {
        tenant_no: sql_params[0] === 1,
        exhibition_nos:
          Array.isArray(sql_params[1]) && sql_params[1].length > 0,
        member_no: sql_params[24] === 10,
        lang_code: sql_params[23] === 'ja',
        limit: sql_params[20] === 20 || sql_params[20] === null,
      }
      console.log('✅ Parameter validation:', paramsMatch)

      const sql = `SELECT * FROM "f_search_auction_items"(${Common.sqlParamNumber(30)});`
      console.log('📝 SQL Query:', sql)
      console.log('🔧 Generated SQL placeholders:', Common.sqlParamNumber(30))
      console.log('📊 SQL params count:', sql_params.length)
      console.log('🎯 Expected params count: 30')
      console.log('🏢 Tenant No:', tenant.tenant_no)
      console.log('🎪 Exhibition Nos:', exhibitionNos)

      // DEBUGGING: Test RLS context before main query
      console.log('🧪 Testing RLS context...')
      return pool
        .rlsQuery(
          tenant.tenant_no,
          "SELECT current_setting('app.current_tenant', true) as current_tenant;",
          []
        )
        .then(rlsTest => {
          console.log('🔍 RLS Context Test Result:', rlsTest)
          if (rlsTest && rlsTest.length > 0) {
            console.log('✅ RLS tenant context:', rlsTest[0].current_tenant)
          } else {
            console.log('❌ RLS context test failed - no result')
          }

          // Test basic data existence before main query
          console.log('🧪 Testing basic data existence...')
          return pool
            .rlsQuery(
              tenant.tenant_no,
              'SELECT COUNT(*) as count FROM t_exhibition_item WHERE exhibition_no IN (67, 100, 133) AND (delete_flag IS NULL OR delete_flag = 0);',
              []
            )
            .then(countTest => {
              console.log('🔍 Exhibition items count test:', countTest)
              if (countTest && countTest.length > 0) {
                console.log('📊 Found exhibition items:', countTest[0].count)
              }

              // Test with minimal parameters first (like our database test)
              console.log('🧪 Testing with minimal parameters first...')
              const minimalParams = [
                1, // in_tenant_no
                [67, 100, 133], // in_exhibition_nos
                null, // in_auction_classification
                null, // in_search_keys
                null, // in_areas
                null, // in_category
                null, // in_start_year
                null, // in_end_year
                null, // in_start_price
                null, // in_end_price
                false, // in_favorite
                false, // in_bidding
                false, // in_un_sold_out
                false, // in_recommending
                false, // in_exceeding_lowest_price
                null, // in_search_results_sort
                null, // in_category_sort
                null, // in_end_datetime_not_extend_sort
                null, // in_ended_datetime_sort
                null, // in_nickname_sort
                20, // in_limit
                null, // in_showed_item_nos
                null, // in_exhibition_item_nos
                'ja', // in_lang_code
                10, // in_member_no
                null, // in_model_list
                null, // in_categories
                null, // in_brand
                null, // in_detail_item_no
                null, // in_brands
              ]

              console.log('🧪 Minimal test parameters:', minimalParams)
              return pool
                .rlsQuery(tenant.tenant_no, sql, minimalParams)
                .then(minimalResult => {
                  console.log('🧪 Minimal test result:', minimalResult)
                  if (
                    minimalResult &&
                    minimalResult.length > 0 &&
                    minimalResult[0].data
                  ) {
                    console.log(
                      '✅ Minimal test SUCCESS - stored procedure works!'
                    )
                    console.log(
                      '📊 Minimal test data:',
                      JSON.stringify(minimalResult[0].data, null, 2)
                    )
                  } else {
                    console.log(
                      '❌ Minimal test FAILED - same issue with minimal params'
                    )
                  }

                  // Now execute the main query with original parameters
                  console.log(
                    '🚀 Executing main query with original parameters...'
                  )
                  return pool.rlsQuery(tenant.tenant_no, sql, sql_params)
                })
            })
        })
        .catch(dbError => {
          console.error('❌ Database query error:', dbError)
          console.error('  - Error message:', dbError.message)
          console.error('  - Error code:', dbError.code)
          console.error('  - SQL:', sql)
          console.error('  - Parameters:', sql_params)
          throw dbError
        })
    })

    .then(result => {
      console.log('☎️ log of query f_search_auction_items : ', result)

      // DEBUGGING: Add comprehensive logging to understand result structure
      console.log('🔍 DEBUG - Result analysis:')
      console.log('  - result type:', typeof result)
      console.log('  - result is array:', Array.isArray(result))
      console.log(
        '  - result.length:',
        result ? result.length : 'null/undefined'
      )

      if (result && result.length > 0) {
        console.log('  - result[0] type:', typeof result[0])
        console.log('  - result[0] keys:', Object.keys(result[0]))
        console.log('  - result[0]:', JSON.stringify(result[0], null, 2))

        // Check for common property names that might contain our data
        const firstRow = result[0]
        if (firstRow.data) {
          console.log('  - result[0].data exists:', typeof firstRow.data)
          console.log(
            '  - result[0].data:',
            JSON.stringify(firstRow.data, null, 2)
          )
        }
        if (firstRow.f_search_auction_items) {
          console.log(
            '  - result[0].f_search_auction_items exists:',
            typeof firstRow.f_search_auction_items
          )
          console.log(
            '  - result[0].f_search_auction_items:',
            JSON.stringify(firstRow.f_search_auction_items, null, 2)
          )
        }

        // Check all properties to find where the data might be
        for (const [key, value] of Object.entries(firstRow)) {
          if (
            value &&
            typeof value === 'object' &&
            (value.items || value.count)
          ) {
            console.log(
              `  - Found potential data in result[0].${key}:`,
              JSON.stringify(value, null, 2)
            )
          }
        }
      } else {
        console.log('  - Result is empty or null!')
      }

      // Try different ways to access the data
      let data = {}

      if (result && result.length > 0) {
        const firstRow = result[0]

        // Method 1: Check if data is in result[0].data
        if (firstRow.data) {
          console.log('📊 Using result[0].data')
          data = firstRow.data
        }
        // Method 2: Check if data is in result[0].f_search_auction_items
        else if (firstRow.f_search_auction_items) {
          console.log('📊 Using result[0].f_search_auction_items')
          data = firstRow.f_search_auction_items
        }
        // Method 3: Check if the first row itself contains the data
        else if (firstRow.items || firstRow.count) {
          console.log('📊 Using result[0] directly')
          data = firstRow
        }
        // Method 4: Check if data is in any property that looks like JSON
        else {
          console.log('📊 Searching for data in all properties')
          for (const [key, value] of Object.entries(firstRow)) {
            if (
              value &&
              typeof value === 'object' &&
              (value.items || value.count)
            ) {
              console.log(`📊 Found data in result[0].${key}`)
              data = value
              break
            }
          }
        }
      }

      console.log('🎯 Final data object:', JSON.stringify(data, null, 2))

      const items = data.items ? data.items : []
      console.log('📦 Final items array:', {
        type: typeof items,
        isArray: Array.isArray(items),
        length: items ? items.length : 'null/undefined',
        sample: items && items.length > 0 ? items[0] : 'no items',
      })

      const isMoreLimit = params.limit && items.length > params.limit
      const response = {
        items,
        count: data.count,
        count_init: data.count_init, // Counting all items when searching by initial conditions
        category_group: data.category_group ? data.category_group : [],
        exhibition_group: data.exhibition_group ? data.exhibition_group : [],
        isMoreLimit,
        moreSearch: params.showedItemNos && params.showedItemNos.length > 0,
      }

      console.log('📤 Final response:', JSON.stringify(response, null, 2))
      return base.createSuccessResponse(cb, response)
    })
    .catch(error => base.createErrorResponse(cb, error))
}
