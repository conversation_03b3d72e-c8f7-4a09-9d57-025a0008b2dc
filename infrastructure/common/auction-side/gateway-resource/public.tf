locals {
  public-resource = {
    path_name = "public"
  }
}
resource "aws_api_gateway_resource" "public-resource" {
  rest_api_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id   = var.root_resource_id
  path_part   = local.public-resource.path_name
}

module "get-new-notices" {
  source = "./get-new-notices"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-exhibition-schedules" {
  source = "./get-exhibition-schedules"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-participation-terms" {
  source = "./get-participation-terms"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-aws-credentials" {
  source = "./get-aws-credentials"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "search-auction-items" {
  source = "./search-auction-items"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "search-stock-items" {
  source = "./search-stock-items"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-item-search-constants" {
  source = "./get-item-search-constants"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}


module "get-item-detail-constants" {
  source = "./get-item-detail-constants"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "download-item-images" {
  source = "./download-item-images"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "download-stock-images" {
  source = "./download-stock-images"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-contact-constants" {
  source = "./get-contact-constants"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-stock-info" {
  source = "./get-stock-info"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-item-info" {
  source = "./get-item-info"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "request-inquiry" {
  source = "./request-inquiry"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "refresh-item" {
  source = "./refresh-item"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "refresh-stock" {
  source = "./refresh-stock"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-related-items" {
  source = "./get-related-items"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-item-inquiry-chat" {
  source = "./get-item-inquiry-chat"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-delivery-inquiry-chat" {
  source = "./get-delivery-inquiry-chat"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-all-successful-bid-history" {
  source = "./get-all-successful-bid-history"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = aws_api_gateway_resource.public-resource.id
  parent_path = "${local.public-resource.path_name}/"
  authorization = "NONE"
  aws_api_gateway_authorizer_id = module.authorizer.aws_api_gateway_authorizer_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}
